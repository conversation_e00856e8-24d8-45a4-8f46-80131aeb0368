namespace HotPreview.Tooling.McpServer.Interfaces;

/// <summary>
/// Interface for executing shell commands.
/// </summary>
public interface ICommandExecutor
{
    /// <summary>
    /// Executes a shell command and returns the standard output as a string.
    /// </summary>
    /// <param name="command">The shell command to be executed.</param>
    /// <returns>The output from the executed command.</returns>
    /// <exception cref="Exception">
    /// Thrown when an error occurs during the command execution process.
    /// </exception>
    string ExecuteCommand(string command);
}
