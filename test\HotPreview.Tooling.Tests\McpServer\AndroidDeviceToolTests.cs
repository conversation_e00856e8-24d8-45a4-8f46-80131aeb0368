using System.Text.Json;
using HotPreview.Tooling.McpServer;
using HotPreview.Tooling.McpServer.Interfaces;
using HotPreview.Tooling.Tests.McpServer.TestHelpers;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;

namespace HotPreview.Tooling.Tests.McpServer;

[TestClass]
public class AndroidDeviceToolTests
{
    private Mock<IProcessService> _mockProcessService = null!;
    private AndroidDeviceTool _tool = null!;
    private ILogger<McpTestClient> _clientLogger = null!;

    [TestInitialize]
    public void Setup()
    {
        _mockProcessService = new Mock<IProcessService>();
        _tool = new AndroidDeviceTool(_mockProcessService.Object);

        ILoggerFactory loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        _clientLogger = loggerFactory.CreateLogger<McpTestClient>();
    }

    [TestMethod]
    public void ListDevices_WithNoDevices_ShouldReturnNoDevicesMessage()
    {
        // Arrange
        // Mock ADB check to fail (simulating ADB not installed)
        _mockProcessService.Setup(x => x.StartProcess("adb version"))
            .Throws(new Exception("ADB not found"));

        // Act
        string result = _tool.ListDevices();

        // Assert
        Assert.IsNotNull(result);
        Assert.IsTrue(result.Contains("ADB is not installed") || result.Contains("Error"));
    }

    [TestMethod]
    public void ListDevices_WithSingleDevice_ShouldParseDeviceCorrectly()
    {
        // Arrange
        // Mock ADB check to fail (simulating ADB not installed)
        _mockProcessService.Setup(x => x.StartProcess("adb version"))
            .Throws(new Exception("ADB not found"));

        // Act
        string result = _tool.ListDevices();

        // Assert
        Assert.IsNotNull(result);
        // Should return error message about ADB not being installed
        Assert.IsTrue(result.Contains("ADB is not installed") || result.Contains("Error"));
    }

    [TestMethod]
    public void ListDevices_WithMultipleDevices_ShouldParseAllDevices()
    {
        // Arrange
        // Mock ADB check to fail (simulating ADB not installed)
        _mockProcessService.Setup(x => x.StartProcess("adb version"))
            .Throws(new Exception("ADB not found"));

        // Act
        string result = _tool.ListDevices();

        // Assert
        Assert.IsNotNull(result);
        // Should return error message about ADB not being installed
        Assert.IsTrue(result.Contains("ADB is not installed") || result.Contains("Error"));
    }

    [TestMethod]
    public void BootDevice_WithValidAvdName_ShouldThrowWhenAdbNotInstalled()
    {
        // Arrange
        string avdName = "test-emulator";
        // Mock ADB check to fail (simulating ADB not installed)
        _mockProcessService.Setup(x => x.StartProcess("adb version"))
            .Throws(new Exception("ADB not found"));

        // Act & Assert - Should throw exception about ADB not being installed
        var exception = Assert.ThrowsException<Exception>(() => _tool.BootDevice(avdName));
        Assert.IsTrue(exception.Message.Contains("ADB is not installed"));
    }

    [TestMethod]
    public void BootDevice_WithEmptyAvdName_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        Assert.ThrowsException<ArgumentNullException>(() => _tool.BootDevice(""));
        Assert.ThrowsException<ArgumentNullException>(() => _tool.BootDevice(null!));
    }

    [TestMethod]
    public void ShutdownDevice_WithValidAvdName_ShouldThrowWhenAdbNotInstalled()
    {
        // Arrange
        string avdName = "test-emulator";
        // Mock ADB check to fail (simulating ADB not installed)
        _mockProcessService.Setup(x => x.StartProcess("adb version"))
            .Throws(new Exception("ADB not found"));

        // Act & Assert - Should throw exception about ADB not being installed
        var exception = Assert.ThrowsException<Exception>(() => _tool.ShutdownDevice(avdName));
        Assert.IsTrue(exception.Message.Contains("ADB is not installed"));
    }

    [TestMethod]
    public void ShutdownDevice_WithEmptyAvdName_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        Assert.ThrowsException<ArgumentNullException>(() => _tool.ShutdownDevice(""));
        Assert.ThrowsException<ArgumentNullException>(() => _tool.ShutdownDevice(null!));
    }

    [TestMethod]
    public void ExecAdb_WithValidParameters_ShouldExecuteCommand()
    {
        // Arrange
        string parameters = "shell input keyevent KEYCODE_HOME";
        _mockProcessService.Setup(x => x.ExecuteCommand($"adb {parameters}"))
            .Returns("Command executed successfully");

        // Act
        string result = _tool.ExecAdb(parameters);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual("Command executed successfully", result);
        _mockProcessService.Verify(x => x.ExecuteCommand($"adb {parameters}"), Times.Once);
    }

    [TestMethod]
    public async Task IntegrationTest_AndroidDeviceToolViaEndToEnd()
    {
        // This is a more comprehensive test that would test the tool through the MCP server
        // Arrange
        McpHttpServerService service = new McpHttpServerService(
            LoggerFactory.Create(builder => builder.AddConsole())
                .CreateLogger<McpHttpServerService>());

        CancellationToken cancellationToken = new CancellationTokenSource(TimeSpan.FromSeconds(10)).Token;

        try
        {
            await service.StartAsync(cancellationToken);

            using HttpClient httpClient = new HttpClient();
            using McpTestClient mcpClient = new McpTestClient(httpClient, _clientLogger);
            httpClient.BaseAddress = new Uri(service.ServerUrl);

            // Act - Call the list devices tool
            JsonDocument response = await mcpClient.CallToolAsync("android_list_devices", new { }, cancellationToken);

            // Assert
            Assert.IsNotNull(response);
            Assert.IsTrue(response.RootElement.TryGetProperty("result", out var result));

            // The tool should return content (even if it's an error about ADB not being installed)
            if (result.TryGetProperty("content", out var content))
            {
                List<JsonElement> contentArray = content.EnumerateArray().ToList();
                Assert.IsTrue(contentArray.Count > 0);

                JsonElement firstContent = contentArray[0];
                Assert.IsTrue(firstContent.TryGetProperty("text", out var text));
                Assert.IsFalse(string.IsNullOrEmpty(text.GetString()));
            }
        }
        finally
        {
            await service.StopAsync(cancellationToken);
        }
    }
}
