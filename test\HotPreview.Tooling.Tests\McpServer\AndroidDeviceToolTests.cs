using System.Text.Json;
using HotPreview.Tooling.McpServer;
using HotPreview.Tooling.McpServer.Interfaces;
using HotPreview.Tooling.Tests.McpServer.TestHelpers;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;

namespace HotPreview.Tooling.Tests.McpServer;

[TestClass]
public class AndroidDeviceToolTests
{
    private Mock<ICommandExecutor> _mockCommandExecutor = null!;
    private AndroidDeviceTool _tool = null!;
    private ILogger<McpTestClient> _clientLogger = null!;

    [TestInitialize]
    public void Setup()
    {
        _mockCommandExecutor = new Mock<ICommandExecutor>();
        _tool = new AndroidDeviceTool(_mockCommandExecutor.Object);

        ILoggerFactory loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        _clientLogger = loggerFactory.CreateLogger<McpTestClient>();
    }

    [TestMethod]
    public void ListDevices_WithNoDevices_ShouldReturnNoDevicesMessage()
    {
        // Arrange
        _mockCommandExecutor.Setup(x => x.ExecuteCommand("adb devices -l"))
            .Returns("List of devices attached\n");

        // Act
        string result = _tool.ListDevices();

        // Assert
        Assert.IsNotNull(result);
        Assert.IsTrue(result.Contains("No devices found"));
    }

    [TestMethod]
    public void ListDevices_WithSingleDevice_ShouldParseDeviceCorrectly()
    {
        // Arrange
        string deviceOutput = """
        List of devices attached
        emulator-5554    device product:sdk_gphone64_x86_64 model:sdk_gphone64_x86_64 device:emu64xa
        """;

        _mockCommandExecutor.Setup(x => x.ExecuteCommand("adb devices -l"))
            .Returns(deviceOutput);

        // Act
        string result = _tool.ListDevices();

        // Assert
        Assert.IsNotNull(result);
        Assert.IsTrue(result.Contains("`emulator-5554`"));
        Assert.IsTrue(result.Contains("`sdk_gphone64_x86_64`"));
        Assert.IsTrue(result.Contains("`emu64xa`"));
    }

    [TestMethod]
    public void ListDevices_WithMultipleDevices_ShouldParseAllDevices()
    {
        // Arrange
        string deviceOutput = """
        List of devices attached
        emulator-5554    device product:sdk_gphone64_x86_64 model:sdk_gphone64_x86_64 device:emu64xa
        RF8N308KFYP      device product:beyond2ltexx model:SM_G975F device:beyond2
        """;

        _mockCommandExecutor.Setup(x => x.ExecuteCommand("adb devices -l"))
            .Returns(deviceOutput);

        // Act
        string result = _tool.ListDevices();

        // Assert
        Assert.IsNotNull(result);
        Assert.IsTrue(result.Contains("`emulator-5554`"));
        Assert.IsTrue(result.Contains("`RF8N308KFYP`"));
        Assert.IsTrue(result.Contains("`beyond2ltexx`"));
        Assert.IsTrue(result.Contains("`SM_G975F`"));
    }

    [TestMethod]
    public void BootDevice_WithValidAvdName_ShouldExecuteAdbCommand()
    {
        // Arrange
        string avdName = "test-emulator";
        _mockCommandExecutor.Setup(x => x.ExecuteCommand($"adb -s {avdName} emu kill"))
            .Returns("");

        // Act & Assert - Should not throw
        _tool.BootDevice(avdName);

        // Verify the command was called
        _mockCommandExecutor.Verify(x => x.ExecuteCommand($"adb -s {avdName} emu kill"), Times.Once);
    }

    [TestMethod]
    public void BootDevice_WithEmptyAvdName_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        Assert.ThrowsException<ArgumentNullException>(() => _tool.BootDevice(""));
        Assert.ThrowsException<ArgumentNullException>(() => _tool.BootDevice(null!));
    }

    [TestMethod]
    public void ShutdownDevice_WithValidAvdName_ShouldExecuteAdbCommand()
    {
        // Arrange
        string avdName = "test-emulator";
        _mockCommandExecutor.Setup(x => x.ExecuteCommand($"adb -s {avdName} emu kill"))
            .Returns("");

        // Act & Assert - Should not throw for valid input
        _tool.ShutdownDevice(avdName);

        // Verify the command was called
        _mockCommandExecutor.Verify(x => x.ExecuteCommand($"adb -s {avdName} emu kill"), Times.Once);
    }

    [TestMethod]
    public void ShutdownDevice_WithEmptyAvdName_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        Assert.ThrowsException<ArgumentNullException>(() => _tool.ShutdownDevice(""));
        Assert.ThrowsException<ArgumentNullException>(() => _tool.ShutdownDevice(null!));
    }

    [TestMethod]
    public void ExecAdb_WithValidParameters_ShouldExecuteCommand()
    {
        // Arrange
        string parameters = "shell input keyevent KEYCODE_HOME";
        _mockCommandExecutor.Setup(x => x.ExecuteCommand($"adb {parameters}"))
            .Returns("Command executed successfully");

        // Act
        string result = _tool.ExecAdb(parameters);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual("Command executed successfully", result);
        _mockCommandExecutor.Verify(x => x.ExecuteCommand($"adb {parameters}"), Times.Once);
    }

    [TestMethod]
    public async Task IntegrationTest_AndroidDeviceToolViaEndToEnd()
    {
        // This is a more comprehensive test that would test the tool through the MCP server
        // Arrange
        McpHttpServerService service = new McpHttpServerService(
            LoggerFactory.Create(builder => builder.AddConsole())
                .CreateLogger<McpHttpServerService>());

        CancellationToken cancellationToken = new CancellationTokenSource(TimeSpan.FromSeconds(10)).Token;

        try
        {
            await service.StartAsync(cancellationToken);

            using HttpClient httpClient = new HttpClient();
            using McpTestClient mcpClient = new McpTestClient(httpClient, _clientLogger);
            httpClient.BaseAddress = new Uri(service.ServerUrl);

            // Act - Call the list devices tool
            JsonDocument response = await mcpClient.CallToolAsync("android_list_devices", new { }, cancellationToken);

            // Assert
            Assert.IsNotNull(response);
            Assert.IsTrue(response.RootElement.TryGetProperty("result", out var result));

            // The tool should return content (even if it's an error about ADB not being installed)
            if (result.TryGetProperty("content", out var content))
            {
                List<JsonElement> contentArray = content.EnumerateArray().ToList();
                Assert.IsTrue(contentArray.Count > 0);

                JsonElement firstContent = contentArray[0];
                Assert.IsTrue(firstContent.TryGetProperty("text", out var text));
                Assert.IsFalse(string.IsNullOrEmpty(text.GetString()));
            }
        }
        finally
        {
            await service.StopAsync(cancellationToken);
        }
    }
}
