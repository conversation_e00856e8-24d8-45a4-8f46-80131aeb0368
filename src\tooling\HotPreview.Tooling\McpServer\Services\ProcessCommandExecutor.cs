using HotPreview.Tooling.McpServer.Helpers;
using HotPreview.Tooling.McpServer.Interfaces;

namespace HotPreview.Tooling.McpServer.Services;

/// <summary>
/// Default implementation of ICommandExecutor that uses the Process helper class.
/// </summary>
public class ProcessCommandExecutor : ICommandExecutor
{
    /// <summary>
    /// Executes a shell command and returns the standard output as a string.
    /// </summary>
    /// <param name="command">The shell command to be executed.</param>
    /// <returns>The output from the executed command.</returns>
    /// <exception cref="Exception">
    /// Thrown when an error occurs during the command execution process.
    /// </exception>
    public string ExecuteCommand(string command)
    {
        return Process.ExecuteCommand(command);
    }
}
